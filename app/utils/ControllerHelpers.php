<?php

/**
 * ControllerHelpers - Common utilities for controllers
 * Provides standardized methods for authentication, validation, and data extraction
 */
class ControllerHelpers
{
    /**
     * Standardized data extraction from request
     * Memory efficient pattern for extracting data from request objects
     * 
     * @param mixed $data Request data object/array
     * @param array $fields Array of field names to extract
     * @param mixed $defaultValue Default value for missing fields
     * @return array Extracted data with consistent structure
     */
    public static function extractRequestData($data, array $fields, $defaultValue = false): array
    {
        $result = [];
        
        // Convert object to array if needed
        if (is_object($data)) {
            $data = (array) $data;
        }
        
        // Extract each field with consistent pattern
        foreach ($fields as $field) {
            $result[$field] = $data[$field] ?? $defaultValue;
        }
        
        return $result;
    }

    /**
     * Extract common authentication headers
     * 
     * @param array $headers Request headers (case-insensitive)
     * @return array Authentication data
     */
    public static function extractAuthHeaders(array $headers): array
    {
        $headers = array_change_key_case($headers, CASE_LOWER);
        
        return [
            'authorization' => $headers['x-authorization'] ?? false,
            'hash_key' => $headers['x-hash-key'] ?? false,
            'app_key' => $headers['x-app-key'] ?? false,
            'access_token' => $headers['x-access'] ?? false
        ];
    }

    /**
     * Validate required authentication parameters
     * 
     * @param array $authData Authentication data from extractAuthHeaders
     * @param array $additionalRequired Additional required fields
     * @return array Validation result with success status and missing fields
     */
    public static function validateAuthParams(array $authData, array $additionalRequired = []): array
    {
        $required = ['authorization', 'hash_key', 'app_key', 'access_token'];
        $required = array_merge($required, $additionalRequired);
        
        $missing = [];
        foreach ($required as $field) {
            if (empty($authData[$field])) {
                $missing[] = $field;
            }
        }
        
        return [
            'valid' => empty($missing),
            'missing_fields' => $missing
        ];
    }

    /**
     * Standardized pagination parameters extraction
     * 
     * @param array $data Request data
     * @param int $defaultLimit Default limit value
     * @return array Pagination parameters
     */
    public static function extractPaginationParams(array $data, int $defaultLimit = 15): array
    {
        $page = $data['page'] ?? 1;
        $limit = $data['limit'] ?? $defaultLimit;
        $export = $data['export'] ?? false;
        
        // Ensure page is at least 1
        $page = max(1, (int) $page);
        
        // Validate limit is numeric and within reasonable bounds
        if (!is_numeric($limit) || $limit < 1) {
            $limit = $defaultLimit;
        }
        
        return [
            'page' => $page,
            'limit' => (int) $limit,
            'export' => $export,
            'offset' => ($page - 1) * $limit
        ];
    }

    /**
     * Extract date range parameters
     * 
     * @param array $data Request data
     * @return array Date range parameters
     */
    public static function extractDateRange(array $data): array
    {
        return [
            'start_date' => $data['start_date'] ?? $data['start'] ?? false,
            'end_date' => $data['end_date'] ?? $data['end'] ?? $data['stop'] ?? false
        ];
    }

    /**
     * Extract partner-related parameters
     * 
     * @param array $data Request data
     * @return array Partner parameters
     */
    public static function extractPartnerParams(array $data): array
    {
        return [
            'partner_id' => $data['partner_id'] ?? false,
            'partner_name' => $data['partner_name'] ?? false,
            'partner_status' => $data['partner_status'] ?? false
        ];
    }

    /**
     * Standardized search parameters extraction
     *
     * @param array $data Request data
     * @return array Search parameters
     */
    public static function extractSearchParams(array $data): array
    {
        return [
            'search' => $data['search'] ?? false,
            'search_field' => $data['search_field'] ?? false,
            'sort' => $data['sort'] ?? 'id',
            'order' => $data['order'] ?? 'DESC',
            'skip_cache' => $data['skipCache'] ?? 1
        ];
    }

    /**
     * Validate partner service data
     *
     * @param array $data Service data to validate
     * @return array Validation result with status and errors
     */
    public static function validatePartnerServiceData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['partner_id'])) {
            $errors[] = 'Partner ID is required';
        } elseif (!is_numeric($data['partner_id'])) {
            $errors[] = 'Partner ID must be numeric';
        }

        if (empty($data['service_id'])) {
            $errors[] = 'Service ID is required';
        } elseif (!is_numeric($data['service_id'])) {
            $errors[] = 'Service ID must be numeric';
        }

        // Optional fields validation
        if (isset($data['rate_limit_per_minute']) && !is_numeric($data['rate_limit_per_minute'])) {
            $errors[] = 'Rate limit per minute must be numeric';
        }

        if (isset($data['status']) && !in_array($data['status'], ['active', 'inactive'])) {
            $errors[] = 'Status must be either active or inactive';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate partner settings data
     *
     * @param array $data Settings data to validate
     * @return array Validation result with status and errors
     */
    public static function validatePartnerSettingsData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['partner_id'])) {
            $errors[] = 'Partner ID is required';
        } elseif (!is_numeric($data['partner_id'])) {
            $errors[] = 'Partner ID must be numeric';
        }

        if (empty($data['api_key'])) {
            $errors[] = 'API key is required';
        } elseif (strlen($data['api_key']) < 32) {
            $errors[] = 'API key must be at least 32 characters long';
        }

        // Optional fields validation
        if (isset($data['ip_address']) && !filter_var($data['ip_address'], FILTER_VALIDATE_IP)) {
            $errors[] = 'Invalid IP address format';
        }

        if (isset($data['callback_url']) && !filter_var($data['callback_url'], FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid callback URL format';
        }

        if (isset($data['billing_mode']) && !in_array($data['billing_mode'], ['prepay', 'postpay'])) {
            $errors[] = 'Billing mode must be either prepay or postpay';
        }

        if (isset($data['rate_limit']) && !is_numeric($data['rate_limit'])) {
            $errors[] = 'Rate limit must be numeric';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate partner bet data
     *
     * @param array $data Bet data to validate
     * @return array Validation result with status and errors
     */
    public static function validatePartnerBetData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['partner_id'])) {
            $errors[] = 'Partner ID is required';
        } elseif (!is_numeric($data['partner_id'])) {
            $errors[] = 'Partner ID must be numeric';
        }

        if (empty($data['profile_id'])) {
            $errors[] = 'Profile ID is required';
        } elseif (!is_numeric($data['profile_id'])) {
            $errors[] = 'Profile ID must be numeric';
        }

        if (empty($data['bet_amount'])) {
            $errors[] = 'Bet amount is required';
        } elseif (!is_numeric($data['bet_amount']) || $data['bet_amount'] <= 0) {
            $errors[] = 'Bet amount must be a positive number';
        }

        if (empty($data['bet_reference'])) {
            $errors[] = 'Bet reference is required';
        }

        if (empty($data['total_games'])) {
            $errors[] = 'Total games is required';
        } elseif (!is_numeric($data['total_games']) || $data['total_games'] <= 0) {
            $errors[] = 'Total games must be a positive number';
        }

        // Optional fields validation
        if (isset($data['bet_currency']) && strlen($data['bet_currency']) !== 3) {
            $errors[] = 'Currency must be a 3-character code';
        }

        if (isset($data['bet_type']) && !is_numeric($data['bet_type'])) {
            $errors[] = 'Bet type must be numeric';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate partner bet slip data
     *
     * @param array $data Bet slip data to validate
     * @return array Validation result with status and errors
     */
    public static function validatePartnerBetSlipData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['partner_id'])) {
            $errors[] = 'Partner ID is required';
        } elseif (!is_numeric($data['partner_id'])) {
            $errors[] = 'Partner ID must be numeric';
        }

        if (empty($data['bet_id'])) {
            $errors[] = 'Bet ID is required';
        } elseif (!is_numeric($data['bet_id'])) {
            $errors[] = 'Bet ID must be numeric';
        }

        if (empty($data['parent_match_id'])) {
            $errors[] = 'Parent match ID is required';
        } elseif (!is_numeric($data['parent_match_id'])) {
            $errors[] = 'Parent match ID must be numeric';
        }

        if (empty($data['market_id'])) {
            $errors[] = 'Market ID is required';
        } elseif (!is_numeric($data['market_id'])) {
            $errors[] = 'Market ID must be numeric';
        }

        if (empty($data['selection_id'])) {
            $errors[] = 'Selection ID is required';
        } elseif (!is_numeric($data['selection_id'])) {
            $errors[] = 'Selection ID must be numeric';
        }

        if (empty($data['odd_value'])) {
            $errors[] = 'Odd value is required';
        } elseif (!is_numeric($data['odd_value']) || $data['odd_value'] <= 0) {
            $errors[] = 'Odd value must be a positive number';
        }

        // Optional fields validation
        if (isset($data['live_bet']) && !in_array($data['live_bet'], [0, 1])) {
            $errors[] = 'Live bet must be 0 or 1';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Build standardized error response for missing authentication
     * 
     * @param string $className Class name for logging
     * @param int $line Line number for logging
     * @param array $missingFields Missing authentication fields
     * @return array Error response structure
     */
    public static function buildAuthErrorResponse(string $className, int $line, array $missingFields = []): array
    {
        $message = empty($missingFields) 
            ? "Authentication required!" 
            : "Missing required fields: " . implode(', ', $missingFields);
            
        return [
            'line_class' => $line . ":" . $className,
            'status_code' => 200,
            'success' => false,
            'error' => [
                'code' => 422,
                'message' => $message
            ]
        ];
    }

    /**
     * Build standardized success response
     * 
     * @param string $className Class name for logging
     * @param int $line Line number for logging
     * @param string $message Success message
     * @param array $data Response data
     * @param float $executionTime Execution time in seconds
     * @return array Success response structure
     */
    public static function buildSuccessResponse(string $className, int $line, string $message, array $data, float $executionTime): array
    {
        return [
            'line_class' => $line . ":" . $className . "| Took " . round($executionTime, 5) . " Sec",
            'status_code' => 200,
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * Build standardized error response
     * 
     * @param string $className Class name for logging
     * @param int $line Line number for logging
     * @param string $message Error message
     * @param int $errorCode Error code
     * @param float $executionTime Execution time in seconds
     * @return array Error response structure
     */
    public static function buildErrorResponse(string $className, int $line, string $message, int $errorCode = 500, float $executionTime = 0): array
    {
        $lineClass = $line . ":" . $className;
        if ($executionTime > 0) {
            $lineClass .= "| Took " . round($executionTime, 5) . " Sec";
        }
        
        return [
            'line_class' => $lineClass,
            'status_code' => 200,
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $message
            ]
        ];
    }

    /**
     * Validate and sanitize partner balance data
     * 
     * @param array $data Partner balance data
     * @return array Validated data
     */
    public static function validatePartnerBalanceData(array $data): array
    {
        return [
            'partner_id' => (int) ($data['partner_id'] ?? 0),
            'balance' => number_format((float) ($data['balance'] ?? 0), 2, '.', ''),
            'bonus' => number_format((float) ($data['bonus'] ?? 0), 2, '.', ''),
            'status' => (int) ($data['status'] ?? 1)
        ];
    }

    /**
     * Build chart data structure for dashboard
     * 
     * @param array $rawData Raw data from database
     * @param string $labelField Field to use for labels
     * @param string $valueField Field to use for values
     * @return array Chart-ready data structure
     */
    public static function buildChartData(array $rawData, string $labelField, string $valueField): array
    {
        $labels = [];
        $values = [];
        
        foreach ($rawData as $item) {
            $labels[] = $item[$labelField] ?? '';
            $values[] = (float) ($item[$valueField] ?? 0);
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'data' => $values,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                ]
            ]
        ];
    }

    /**
     * Extract bet slip parameters
     *
     * @param array $data Request data
     * @return array Bet slip parameters
     */
    public static function extractBetSlipParams(array $data): array
    {
        return [
            'bet_id' => $data['bet_id'] ?? false,
            'sport_id' => $data['sport_id'] ?? false,
            'market_id' => $data['market_id'] ?? false,
            'selection_id' => $data['selection_id'] ?? false,
            'status' => $data['status'] ?? false,
            'live_bet' => $data['live_bet'] ?? false
        ];
    }

    /**
     * Complete authentication and authorization flow
     *
     * @param array $authData Authentication headers from extractAuthHeaders
     * @param string $permissionName Required permission name
     * @param array $additionalRequired Additional required fields beyond standard auth
     * @return array Result with success status, user data, or error response
     */
    public static function authenticateAndAuthorize(array $authData, string $permissionName, array $additionalRequired = []): array
    {
        // Validate authentication parameters
        $authValidation = self::validateAuthParams($authData, $additionalRequired);
        if (!$authValidation['valid']) {
            return [
                'success' => false,
                'error_type' => 'validation',
                'error_code' => 422,
                'message' => 'Authentication information is incomplete. Please check your request and try again.',
                'technical_message' => 'Missing required fields: ' . implode(', ', $authValidation['missing_fields'])
            ];
        }

        // Authenticate user token using unified authentication
        try {
            $authResponse = UserUtils::QuickAuthenticate($authData['access_token']);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return [
                    'success' => false,
                    'error_type' => 'authentication',
                    'error_code' => 401,
                    'message' => 'Your session has expired or is invalid. Please log in again.',
                    'technical_message' => $authResponse['message'] ?? 'Authentication failed'
                ];
            }
        } catch (Exception $ex) {
            return [
                'success' => false,
                'error_type' => 'authentication',
                'error_code' => 401,
                'message' => 'Unable to verify your identity. Please try logging in again.',
                'technical_message' => 'Authentication service error'
            ];
        }

        // Check user permissions
        try {
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return [
                    'success' => false,
                    'error_type' => 'authorization',
                    'error_code' => 403,
                    'message' => 'You do not have permission to perform this action. Contact your administrator if you believe this is an error.',
                    'technical_message' => "Missing permission: $permissionName"
                ];
            }
        } catch (Exception $ex) {
            return [
                'success' => false,
                'error_type' => 'authorization',
                'error_code' => 403,
                'message' => 'Unable to verify your permissions. Please try again or contact support.',
                'technical_message' => 'Permission validation error'
            ];
        }

        // Success - return comprehensive user data including partner information
        return [
            'success' => true,
            'user_data' => $authResponse['data'],
            'user_id' => $authResponse['data']['user_id'],
            'user_name' => $authResponse['data']['user_name'],
            'user_type' => $authResponse['data']['user_type'],
            'role_id' => $authResponse['data']['role_id'] ?? null,
            'permission_acl' => $authResponse['data']['permission_acl'] ?? null,
            'partner_count' => $authResponse['data']['partner_count'] ?? 0,
            'partners' => $authResponse['data']['partners'] ?? []
        ];
    }

    /**
     * Build user-friendly error response from authentication result
     *
     * @param array $authResult Result from authenticateAndAuthorize
     * @param string $className Class name for logging
     * @param int $line Line number for logging
     * @return array Error response structure
     */
    public static function buildAuthFailureResponse(array $authResult, string $className, int $line): array
    {
        return [
            'line_class' => $line . ":" . $className,
            'status_code' => 200,
            'success' => false,
            'error' => [
                'code' => $authResult['error_code'],
                'message' => $authResult['message'],
                'type' => $authResult['error_type']
            ],
            'technical_details' => $authResult['technical_message'] ?? null
        ];
    }

    /**
     * User-friendly error messages mapping
     *
     * @param string $technicalError Technical error message
     * @param string $context Context of the error (e.g., 'database', 'validation', 'processing')
     * @return string User-friendly error message
     */
    public static function getUserFriendlyErrorMessage(string $technicalError, string $context = 'general'): string
    {
        $errorMappings = [
            'database' => [
                'connection' => 'We are experiencing technical difficulties. Please try again in a few moments.',
                'timeout' => 'The request is taking longer than expected. Please try again.',
                'constraint' => 'This action cannot be completed due to data requirements. Please check your input.',
                'duplicate' => 'This information already exists in our system.',
                'not_found' => 'The requested information could not be found.',
                'default' => 'We encountered an issue while processing your request. Please try again.'
            ],
            'validation' => [
                'required' => 'Please fill in all required fields.',
                'format' => 'Please check the format of your input and try again.',
                'range' => 'The value you entered is outside the acceptable range.',
                'default' => 'Please check your input and try again.'
            ],
            'processing' => [
                'timeout' => 'Your request is taking longer than expected. Please try again.',
                'limit' => 'You have reached the limit for this action. Please try again later.',
                'conflict' => 'This action conflicts with another operation. Please try again.',
                'default' => 'We encountered an issue while processing your request. Please try again.'
            ],
            'general' => [
                'default' => 'Something went wrong. Please try again or contact support if the problem persists.'
            ]
        ];

        $contextMappings = $errorMappings[$context] ?? $errorMappings['general'];

        // Try to match technical error to user-friendly message
        $lowerError = strtolower($technicalError);

        foreach ($contextMappings as $key => $message) {
            if ($key !== 'default' && strpos($lowerError, $key) !== false) {
                return $message;
            }
        }

        return $contextMappings['default'];
    }

    /**
     * Build standardized error response with user-friendly messages
     *
     * @param string $className Class name for logging
     * @param int $line Line number for logging
     * @param string $technicalMessage Technical error message
     * @param string $context Error context
     * @param int $errorCode HTTP error code
     * @param float $executionTime Execution time in seconds
     * @return array Error response structure
     */
    public static function buildUserFriendlyErrorResponse(string $className, int $line, string $technicalMessage, string $context = 'general', int $errorCode = 500, float $executionTime = 0): array
    {
        $userMessage = self::getUserFriendlyErrorMessage($technicalMessage, $context);

        $lineClass = $line . ":" . $className;
        if ($executionTime > 0) {
            $lineClass .= "| Took " . round($executionTime, 5) . " Sec";
        }

        return [
            'line_class' => $lineClass,
            'status_code' => 200,
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $userMessage,
                'type' => $context
            ],
            'technical_details' => $technicalMessage
        ];
    }

    /**
     * Validate bulk export request
     *
     * @param array $data Request data
     * @param string $userRole User role
     * @param int $userId User ID
     * @return array Validation result with status and errors
     */
    public static function validateBulkExportRequest(array $data, string $userRole, int $userId): array
    {
        $errors = [];
        $warnings = [];

        // Check if export is requested
        $export = $data['export'] ?? false;
        if (!$export) {
            return ['valid' => true, 'errors' => [], 'warnings' => []];
        }

        // Role-based export limits
        $roleExportLimits = [
            'super_admin' => 500000,
            'admin' => 100000,
            'manager' => 50000,
            'user' => 10000
        ];

        $maxExportLimit = $roleExportLimits[$userRole] ?? $roleExportLimits['user'];

        // Check date range for export (prevent too large exports)
        $startDate = $data['start_date'] ?? $data['start'] ?? false;
        $endDate = $data['end_date'] ?? $data['end'] ?? false;

        if ($startDate && $endDate) {
            $start = new DateTime($startDate);
            $end = new DateTime($endDate);
            $daysDiff = $start->diff($end)->days;

            // Limit export date range based on role
            $maxDaysForRole = [
                'super_admin' => 365,
                'admin' => 180,
                'manager' => 90,
                'user' => 30
            ];

            $maxDays = $maxDaysForRole[$userRole] ?? $maxDaysForRole['user'];

            if ($daysDiff > $maxDays) {
                $errors[] = "Export date range cannot exceed $maxDays days for your role";
            }
        } else {
            // If no date range specified, limit to recent data
            $defaultDaysForRole = [
                'super_admin' => 90,
                'admin' => 60,
                'manager' => 30,
                'user' => 7
            ];

            $defaultDays = $defaultDaysForRole[$userRole] ?? $defaultDaysForRole['user'];
            $warnings[] = "No date range specified. Export limited to last $defaultDays days";
        }

        // Check for partner_id filter (required for non-admin users)
        if (!in_array($userRole, ['super_admin', 'admin']) && empty($data['partner_id'])) {
            $errors[] = 'Partner ID filter is required for bulk exports';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'max_export_limit' => $maxExportLimit
        ];
    }

    /**
     * Log bulk export activity
     *
     * @param array $params Export parameters
     * @return bool Success status
     */
    public static function logBulkExportActivity(array $params): bool
    {
        try {
            // Log export activity for audit purposes
            $logData = [
                'user_id' => $params['user_id'],
                'export_type' => $params['export_type'],
                'filters' => json_encode($params['filters']),
                'record_count' => $params['record_count'],
                'ip_address' => $params['ip_address'],
                'user_agent' => $params['user_agent'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ];

            // This would typically insert into an audit log table
            // For now, we'll just return true
            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Apply export security headers
     *
     * @param array $data Export data
     * @param string $filename Suggested filename
     * @return array Headers to apply
     */
    public static function getExportSecurityHeaders(array $data, string $filename): array
    {
        return [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY'
        ];
    }
}
