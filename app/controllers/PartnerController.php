<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * Description of PartnerController
 *
 * <AUTHOR>
 */
class PartnerController extends \ControllerBase
{

    /**
     * GetPartners
     * @return type
     */
    public function GetPartners()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Partners";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartners :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'name', 'limit', 'skip_cache', 'sort', 'page', 'export', 'status', 'start', 'end'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $params['limit'] = $extractedData['limit'];
            $params['skipCache'] = $extractedData['skip_cache'];
            $params['sort'] = $extractedData['sort'];
            $page = $extractedData['page'];
            $export = $extractedData['export'];
            $status = $extractedData['status'];
            $start = $extractedData['start'];
            $stop = $extractedData['end'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            if (!$page) {
                $page = 1;
            }

            if (!$params['limit'] || !is_numeric($params['limit'])) {
                $params['limit'] = $this->settings['SelectRecordLimit'];
            }

            if (!in_array($params['skipCache'], [1, 2])) {
                $params['skipCache'] = 1;
            }

            $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
            if (count($order_arr) > 1) {
                $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
                $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

                if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                    $order = 'DESC';
                }
            } else {
                $sort = 'p.id';
                $order = 'DESC';
            }

            // Set default values
            if (!$page) {
                $page = 1;
            }

            if (!$params['limit'] || !is_numeric($params['limit'])) {
                $params['limit'] = $this->settings['SelectRecordLimit'];
            }

            if (!in_array($params['skipCache'], [1, 2])) {
                $params['skipCache'] = 1;
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND p.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $orderBy = $sort ? "ORDER BY $sort $order" : "";
                $exportLimit = 50000;
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // Query
            $sql = "SELECT (SELECT COUNT(p.id) FROM partners p  $searchQuery) as trx_count,"
                . "p.id, p.name, p.status, p.email_address, p.dial_code, p.msisdn, p.address, p.country, p.created_at, p.updated_at "
                . "FROM partners p $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Partners!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Partners successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartner
     * @return type
     */
    public function CreatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request CreatePartner :" . json_encode($data));

        $timestamp = $data->timestamp ?? false;
        $name = $data->name ?? false;
        $user_id = $data->user_id ?? false;
        $email_address = $data->email_address ?? false;
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;
        $dial_code = $data->dial_code ?? null;
        $status = $data->status ?? 1;

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'name', 'user_id', 'email_address', 'address', 'country', 'msisdn', 'dial_code', 'status'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Check if partner name already exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE name = :name", [':name' => $name]);

            if ($existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner name already exists!'], true);
            }

            // Insert new partner
            $partnerId = $this->rawInsertBulk('dbUser', 'partners', [
                'name' => $name,
                'user_id' => $user_id,
                'email_address' => $email_address,
                'address' => $address,
                'country' => $country,
                'msisdn' => $msisdn,
                'dial_code' => $dial_code,
                'status' => $status,
                'created_by' => $authResult['user_id'],
                'created_at' => $this->now(),
                'updated_at' => $this->now(),
            ]);

            if (!$partnerId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner created successfully!',
                    'data' => ['partner_id' => $partnerId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartner
     * @return type
     */
    public function UpdatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartner :" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data->timestamp ?? false;

        $partnerId = $data->partner_id ?? false;
        $name = $data->name ?? false;
        $status = $data->status ?? false;
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Partner ID is required!"], true);
        }

        // Validate status if provided
        if ($status && !in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Get authenticated user data
            $userData = $authResult['user_data'];

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if new name conflicts with existing partner (if name is being updated)
            if ($name && $name !== $existingPartner['name']) {
                $nameConflict = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM partners WHERE name = :name AND id != :id",
                    [':name' => $name, ':id' => $partnerId]);

                if ($nameConflict) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful',
                        ['code' => 409, 'message' => 'Partner name already exists!'], true);
                }
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerId];

            if ($name) {
                $updateFields[] = "name = :name";
                $updateParams[':name'] = $name;
            }
            if ($status) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }
            if ($address !== null) {
                $updateFields[] = "address = :address";
                $updateParams[':address'] = $address;
            }
            if ($country !== null) {
                $updateFields[] = "country = :country";
                $updateParams[':country'] = $country;
            }
            if ($msisdn !== null) {
                $updateFields[] = "msisdn = :msisdn";
                $updateParams[':msisdn'] = $msisdn;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partners SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     *
     *  PartnerServices
     *
    **/

    /**
     * GetPartnerServices - Get services for a specific partner
     * @return type
     */
    function GetPartnerServices()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Services";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_id',  'status', 'skip_cache'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
//            $pagination = ControllerHelpers::buildPaginationParams($data);
//            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'ps.id', 'DESC');

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_services', [
                'partner_id' => $extractedData['partner_id'],
                'status' => $extractedData['status'],
//                'page' => $pagination['page'],
//                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            $skipCache = $extractedData['skip_cache'] ?: 0;
//            if ($skipCache != 1 && $pagination['export'] != 1) {
//                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
//                if ($cachedResults) {
//                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
//                        200, 'Request is successful',
//                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner services successfully! (cached)',
//                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
//                }
//            }

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($extractedData['partner_id']) {
                $searchQuery .= " AND ps.partner_id = :partner_id";
                $queryParams[':partner_id'] = $extractedData['partner_id'];
            }

            if ($extractedData['status'] !== false) {
                $searchQuery .= " AND ps.status = :status";
                $queryParams[':status'] = $extractedData['status'];
            }

//            if ($pagination['export'] == 1) {
//                $sorting = "";
//            }

            // Execute query
            $query = "SELECT (SELECT COUNT(ps.id) FROM partner_services ps $searchQuery) as service_count,
                      ps.id, ps.partner_id, p.name as partner_name, s.name as service_name,
                      ps.rate_limit_per_minute, ps.status, ps.created_at
                      FROM partner_services ps
                      LEFT JOIN services s ON ps.service_id = s.id
                      LEFT JOIN partners p ON ps.partner_id = p.id
                      $searchQuery";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    [
                        'code' => 404,
                        'message' => 'No partner services found.'
                    ],
                    true
                );
            }

            // Mask sensitive data
            foreach ($results as &$result) {
                if (isset($result['api_key'])) {
                    $result['api_key'] = $this->maskApiKey($result['api_key']);
                }
            }

            // Cache results for future requests (only if not export and not skip_cache)
//            if ($skipCache != 1 && $pagination['export'] != 1) {
//                $cacheData = [
//                    'record_count' => $results[0]['service_count'],
//                    'result' => $results
//                ];
//                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(15)); // 15 minutes cache
//            }

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['service_count'] . ' partner services successfully!',
                    'data' => [
                        'record_count' => $results[0]['service_count'],
                        'result' => $results
                    ]
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(
                __LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                [
                    'code' => 500,
                    'message' => "Internal Server Error."
                ],
                true
            );
        }
    }

    /**
     * CreatePartnerService - Create a new partner service
     * @return type
     */
    function CreatePartnerService()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Service";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerService :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_id', 'service_id', 'rate_limit_per_minute', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            // Validate input data
            $validation = ControllerHelpers::validatePartnerServiceData($extractedData);
            if (!$validation['valid']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Validation failed', 'errors' => $validation['errors']], true);
            }

            $partnerId = $extractedData['partner_id'];
            $serviceId = $extractedData['service_id'];
            $rateLimitPerMinute = $extractedData['rate_limit_per_minute'] ?: 60;
            $status = $extractedData['status'] ?: 'active';

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id",
                [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if service exists
            $existingService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM services WHERE id = :id",
                [':id' => $serviceId]);

            if (!$existingService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Service not found!'], true);
            }

            // Check if partner service already exists
            $existingPartnerService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_services WHERE partner_id = :partner_id AND service_id = :service_id",
                [':partner_id' => $partnerId, ':service_id' => $serviceId]);

            if ($existingPartnerService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner service already exists!'], true);
            }

            // Insert new partner service
            $partnerServiceId = $this->rawInsertBulk('dbUser', 'partner_services', [
                'partner_id' => $partnerId,
                'service_id' => $serviceId,
                'rate_limit_per_minute' => $rateLimitPerMinute,
                'status' => $status,
                'created_at' => $this->now()
            ]);

            if (!$partnerServiceId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner service!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerServicesCache($partnerId);
            RedisUtils::invalidatePartnerCache($partnerId);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner service created successfully!',
                    'data' => ['partner_service_id' => $partnerServiceId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerService - Update an existing partner service
     * @param int $partnerServiceId
     * @return type
     */
    function UpdatePartnerService($partnerServiceId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Service";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerService :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'rate_limit_per_minute', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            $rateLimitPerMinute = $extractedData['rate_limit_per_minute'];
            $status = $extractedData['status'];

            // Check if partner service exists
            $existingPartnerService = $this->rawSelectOneRecord('dbUser',
                "SELECT id, partner_id FROM partner_services WHERE id = :id",
                [':id' => $partnerServiceId]);

            if (!$existingPartnerService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner service not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerServiceId];

            if ($rateLimitPerMinute !== null) {
                if (!is_numeric($rateLimitPerMinute)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Rate limit per minute must be numeric!'], true);
                }
                $updateFields[] = "rate_limit_per_minute = :rate_limit_per_minute";
                $updateParams[':rate_limit_per_minute'] = $rateLimitPerMinute;
            }

            if ($status !== null) {
                if (!in_array($status, ['active', 'inactive'])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Status must be either active or inactive!'], true);
                }
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateFields[] = "updated_at = :updated_at";
            $updateParams[':updated_at'] = $this->now();

            $updateSql = "UPDATE partner_services SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner service!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerServicesCache($existingPartnerService['partner_id'], $partnerServiceId);
            RedisUtils::invalidatePartnerCache($existingPartnerService['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner service updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     *
     *  Partner Settings
     *
     **/


    /**
     * GetPartnerSettings
     * @return type
     */
    public function GetPartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Settings";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerSettings :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_id',  'status', 'skip_cache'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'ps.id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            if ($params['partner_id']) {
                $searchParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND ps.partner_id = :partner_id";
            }

            if ($params['name']) {
                $searchParams[':name'] = $params['name'];
                $searchQuery .= " AND pbs.name = :name";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(ps.id) FROM partners_settings ps $searchQuery) as trx_count,
                    ps.id as setting_id, ps.partner_id, ps., p.name as partner_name, ps.api_key,ps.ip_address, ps.callback_url, 
                    ps.currency, ps.denomination, ps.timezone, ps.billing_mode, ps.rate_limit, ps.websites, ps.version, 
                    ps.updated_by, ps.created_at, ps.updated_at
                    FROM partners_settings ps
                    LEFT JOIN partners p ON pbs.partner_id = p.id
                    $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found.'], true);
            }

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bet slips successfully!',
                    'data' =>
                        [
                            'record_count' => $results[0]['trx_count'],
                            'result' => $results
                        ]
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerSettings
     * @return type
     */
    public function CreatePartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request CreatePartnerSettings :" . json_encode($data));

//        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
//        $Authorization = $headers['x-authorization'] ?? false;
//        $hashKey = $headers['x-hash-key'] ?? false;
//        $appKey = $headers['x-app-key'] ?? false;
//        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $apiKey = $data->api_key ?? false;
        $ipAddress = $data->ip_address ?? null;
        $callbackUrl = $data->callback_url ?? null;
        $currency = $data->currency ?? 'USD';
        $denomination = $data->denomination ?? 'cents';
        $timezone = $data->timezone ?? 'UTC';
        $billingMode = $data->billing_mode ?? 'prepay';
        $rateLimit = $data->rate_limit ?? 60;
        $websites = $data->websites ?? null;
        $version = $data->version ?? 1;
        $timestamp = $data->timestamp ?? false;

        // Validate billing mode
        if (!in_array($billingMode, ['prepay', 'postpay'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid billing mode. Must be 'prepay' or 'postpay'"], true);
        }

        // Validate rate limit
        if (!is_numeric($rateLimit) || $rateLimit < 1 || $rateLimit > 10000) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 10000"], true);
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'api_key', 'ip_address', 'callback_url', 'currency', 'denomination', 'timezone', 'billing_mode', 'rate_limit', 'websites', 'version'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate partner exists
            $partner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 1", [':id' => $partnerId]);

            if (!$partner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Check if partner settings already exist
            $existingSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE partner_id = :partner_id",
                [':partner_id' => $partnerId]);

            if ($existingSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner settings already exist!'], true);
            }

            // Check if API key is unique
            $existingApiKey = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE api_key = :api_key",
                [':api_key' => $apiKey]);

            if ($existingApiKey) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'API key already exists!'], true);
            }

            // Insert new partner settings
            $partnerSettingsId = $this->rawInsertBulk('dbUser', 'partner_settings', [
                'partner_id' => $partnerId,
                'api_key' => $apiKey,
                'ip_address' => $ipAddress,
                'callback_url' => $callbackUrl,
                'currency' => $currency,
                'denomination' => $denomination,
                'timezone' => $timezone,
                'billing_mode' => $billingMode,
                'rate_limit' => $rateLimit,
                'websites' => $websites ? json_encode($websites) : null,
                'version' => $version,
                'updated_by' => $authResult['user_id'],
                'created_at' => $this->now(),
                'updated_at' => $this->now()
            ]);

            if (!$partnerSettingsId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner settings created successfully!',
                    'data' => ['partner_settings_id' => $partnerSettingsId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerSettings
     * @return type
     */
    public function UpdatePartnerSettings($partnerSettingsId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerSettings :" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data->timestamp ?? false;

        $ipAddress = $data->ip_address ?? null;
        $callbackUrl = $data->callback_url ?? null;
        $currency = $data->currency ?? null;
        $denomination = $data->denomination ?? null;
        $timezone = $data->timezone ?? null;
        $billingMode = $data->billing_mode ?? null;
        $rateLimit = $data->rate_limit ?? null;
        $websites = $data->websites ?? null;
        $version = $data->version ?? null;

        // Validate billing mode if provided
        if ($billingMode !== null && !in_array($billingMode, ['prepay', 'postpay'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid billing mode. Must be 'prepay' or 'postpay'"], true);
        }

        // Validate rate limit if provided
        if ($rateLimit !== null && (!is_numeric($rateLimit) || $rateLimit < 1 || $rateLimit > 10000)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 10000"], true);
        }

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Get authenticated user data
            $userData = $authResult['user_data'];

            // Validate partner settings exist
            $partnerSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE id = :id", [':id' => $partnerSettingsId]);

            if (!$partnerSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner settings not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerSettingsId];

            if ($ipAddress !== null) {
                $updateFields[] = "ip_address = :ip_address";
                $updateParams[':ip_address'] = $ipAddress;
            }
            if ($callbackUrl !== null) {
                $updateFields[] = "callback_url = :callback_url";
                $updateParams[':callback_url'] = $callbackUrl;
            }
            if ($currency !== null) {
                $updateFields[] = "currency = :currency";
                $updateParams[':currency'] = $currency;
            }
            if ($denomination !== null) {
                $updateFields[] = "denomination = :denomination";
                $updateParams[':denomination'] = $denomination;
            }
            if ($timezone !== null) {
                $updateFields[] = "timezone = :timezone";
                $updateParams[':timezone'] = $timezone;
            }
            if ($billingMode !== null) {
                $updateFields[] = "billing_mode = :billing_mode";
                $updateParams[':billing_mode'] = $billingMode;
            }
            if ($rateLimit !== null) {
                $updateFields[] = "rate_limit = :rate_limit";
                $updateParams[':rate_limit'] = $rateLimit;
            }
            if ($websites !== null) {
                $updateFields[] = "websites = :websites";
                $updateParams[':websites'] = json_encode($websites);
            }
            if ($version !== null) {
                $updateFields[] = "version = :version";
                $updateParams[':version'] = $version;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partner_settings SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner settings updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     * Mask API key for security
     * @param string $apiKey
     * @return string
     */
    private function maskApiKey($apiKey)
    {
        if (strlen($apiKey) <= 8) {
            return str_repeat('*', strlen($apiKey));
        }
        return substr($apiKey, 0, 4) . str_repeat('*', strlen($apiKey) - 8) . substr($apiKey, -4);
    }
}