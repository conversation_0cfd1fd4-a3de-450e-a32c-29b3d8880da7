<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * SystemController - Handles system-level operations like roles and permissions
 *
 * <AUTHOR>
 */
class SystemController extends \ControllerBase
{

    /**
     * GetRoles - Retrieve all user roles
     * @return type
     */
    function GetRoles()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View System Roles";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'status', 'search'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $status = $extractedData['status'] ?: 1;
            $search = $extractedData['search'] ?: '';

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Build query conditions
            $conditions = "WHERE status = :status";
            $params = [':status' => $status];

            if (!empty($search)) {
                $conditions .= " AND (name LIKE :search OR description LIKE :search)";
                $params[':search'] = "%$search%";
            }

            $roles = $this->rawSelect('dbUser',
                "SELECT id, name, description, permissions_acl, status
                 FROM user_roles $conditions ORDER BY name ASC", $params);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . "| SQL:" . "SELECT id, name, description, permissions_acl, status
                 FROM user_roles $conditions ORDER BY name ASC"
                . "| Params:" . json_encode($params)
                . "| Results==>" . json_encode($roles));

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Roles retrieved successfully!',
                    'data' => $roles ?: [],
                    'total' => count($roles ?: [])
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreateRole - Create a new user role
     * @return type
     */
    function CreateRole()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create System Roles";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'name', 'description', 'permissions_acl'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (empty($extractedData['name'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Role name is required!'], true);
            }

            // Check if role already exists
            $existingRole = $this->rawSelectOneRecord(
                'dbUser',
                "SELECT id, name FROM user_roles WHERE name LIKE :name AND status = 1 LIMIT 1",
                [':name' => '%' . $extractedData['name'] . '%']
            );

            if ($existingRole) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    [
                        'code' => 409,
                        'message' => 'Role with a similar name already exists! '.$existingRole['name'],
//                        'existing_role' => $existingRole['name']
                    ],
                    true
                );
            }


            // Create new role
            $roleId = $this->rawInsertBulk('dbUser', 'user_roles', [
                'name' => $extractedData['name'],
                'description' => $extractedData['description'] ?: $extractedData['name'],
                'permissions_acl' => $extractedData['permissions_acl'] ?: '',
                'status' => 1,
                'created_by' => $authResult['user_id'],
                'updated_by' => $authResult['user_id'],
//                'created_at' => $this->now()
            ]);

            if (!$roleId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create role!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Role created successfully!',
                    'data' => ['role_id' => $roleId]
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateRole - Update an existing user role
     * @return type
     */
    function UpdateRole()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update System Roles";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'role_id', 'name', 'description', 'permissions_acl', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (empty($extractedData['role_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Role ID is required!'], true);
            }

            // Check if role exists
            $existingRole = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM user_roles WHERE id = :id",
                [':id' => $extractedData['role_id']]);

            if (!$existingRole) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Role not found!'], true);
            }

            // Build update data
            $updateData = ['updated_at' => $this->now()];
            if (!empty($extractedData['name'])) $updateData['name'] = $extractedData['name'];
            if (!empty($extractedData['description'])) $updateData['description'] = $extractedData['description'];
            if (isset($extractedData['permissions_acl'])) $updateData['permissions_acl'] = $extractedData['permissions_acl'];
            if (isset($extractedData['status'])) $updateData['status'] = $extractedData['status'];

            // Update role
            $updated = $this->rawUpdateBulk('dbUser', 'user_roles', $updateData, "id = :id", [':id' => $extractedData['role_id']]);

            if (!$updated) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update role!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Role updated successfully!',
                    'data' => ['role_id' => $extractedData['role_id']]
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeleteRole - Delete a user role
     * @return type
     */
    function DeleteRole()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete System Roles";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'role_id'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (empty($extractedData['role_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Role ID is required!'], true);
            }

            // Check if role exists and is not Super Administrator
            $existingRole = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM user_roles WHERE id = :id",
                [':id' => $extractedData['role_id']]);

            if (!$existingRole) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Role not found!'], true);
            }

            if ($existingRole['name'] === 'Super Administrator') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Cannot delete Super Administrator role!'], true);
            }

            // Check if role is in use
            $roleInUse = $this->rawSelectOneRecord('dbUser',
                "SELECT COUNT(*) as user_count FROM user_login WHERE role_id = :role_id AND status = 1",
                [':role_id' => $extractedData['role_id']]);

            if ($roleInUse['user_count'] > 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Cannot delete role that is assigned to users!'], true);
            }

            // Soft delete role
            $deleted = $this->rawUpdateBulk('dbUser', 'user_roles', 
                ['status' => 0, 'updated_at' => $this->now()], 
                "id = :id", [':id' => $extractedData['role_id']]);

            if (!$deleted) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete role!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Role deleted successfully!',
                    'data' => ['role_id' => $extractedData['role_id']]
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPermissions - Retrieve all user permissions
     * @return type
     */
    function GetPermissions()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View System Permissions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'status', 'search'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $status = $extractedData['status'] ?: 1;
            $search = $extractedData['search'] ?: '';

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Build query conditions
            $conditions = "WHERE status = :status";
            $params = [':status' => $status];

            if (!empty($search)) {
                $conditions .= " AND (name LIKE :search OR description LIKE :search)";
                $params[':search'] = "%$search%";
            }

            $permissions = $this->rawSelect('dbUser',
                "SELECT id, name, description, status, created_at, updated_at
                 FROM user_permissions $conditions ORDER BY name ASC", $params);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Permissions retrieved successfully!',
                    'data' => $permissions ?: [],
                    'total' => count($permissions ?: [])
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePermission - Create a new user permission
     * @return type
     */
    function CreatePermission()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create System Permissions";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'name', 'description'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (empty($extractedData['name'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Permission name is required!'], true);
            }

            // Check if permission already exists
            $existingPermission = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM user_permissions WHERE name = :name AND status = 1",
                [':name' => $extractedData['name']]);

            if ($existingPermission) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Permission with this name already exists!'], true);
            }

            // Create new permission
            $permissionId = $this->rawInsertBulk('dbUser', 'user_permissions', [
                'name' => $extractedData['name'],
                'description' => $extractedData['description'] ?: $extractedData['name'],
                'status' => 1,
                'created_at' => $this->now()
            ]);

            if (!$permissionId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create permission!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Permission created successfully!',
                    'data' => ['permission_id' => $permissionId]
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePermission - Update an existing user permission
     * @return type
     */
    function UpdatePermission()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update System Permissions";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'permission_id', 'name', 'description', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (empty($extractedData['permission_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Permission ID is required!'], true);
            }

            // Check if permission exists
            $existingPermission = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM user_permissions WHERE id = :id",
                [':id' => $extractedData['permission_id']]);

            if (!$existingPermission) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Permission not found!'], true);
            }

            // Build update data
            $updateData = ['updated_at' => $this->now()];
            if (!empty($extractedData['name'])) $updateData['name'] = $extractedData['name'];
            if (!empty($extractedData['description'])) $updateData['description'] = $extractedData['description'];
            if (isset($extractedData['status'])) $updateData['status'] = $extractedData['status'];

            // Update permission
            $updated = $this->rawUpdateBulk('dbUser', 'user_permissions', $updateData,
                "id = :id", [':id' => $extractedData['permission_id']]);

            if (!$updated) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update permission!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Permission updated successfully!',
                    'data' => ['permission_id' => $extractedData['permission_id']]
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeletePermission - Delete a user permission
     * @return type
     */
    function DeletePermission()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete System Permissions";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'permission_id'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (empty($extractedData['permission_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Permission ID is required!'], true);
            }

            // Check if permission exists
            $existingPermission = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM user_permissions WHERE id = :id",
                [':id' => $extractedData['permission_id']]);

            if (!$existingPermission) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Permission not found!'], true);
            }

            // Check if permission is in use by roles
            $permissionInUse = $this->rawSelectOneRecord('dbUser',
                "SELECT COUNT(*) as role_count FROM user_roles
                 WHERE FIND_IN_SET(:permission_id, permissions_acl) > 0 AND status = 1",
                [':permission_id' => $extractedData['permission_id']]);

            if ($permissionInUse['role_count'] > 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Cannot delete permission that is assigned to roles!'], true);
            }

            // Soft delete permission
            $deleted = $this->rawUpdateBulk('dbUser', 'user_permissions',
                ['status' => 0, 'updated_at' => $this->now()],
                "id = :id", [':id' => $extractedData['permission_id']]);

            if (!$deleted) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete permission!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Permission deleted successfully!',
                    'data' => ['permission_id' => $extractedData['permission_id']]
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

}
