<?php

require_once 'app/utils/ControllerHelpers.php';

class Bets<PERSON>ontroller extends \ControllerBase
{

    /**
     * CreatePartnerBet - Create a new partner bet
     * @return type
     */
    function CreatePartnerBet()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Bet";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerBet :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_id', 'profile_id', 'bet_currency', 'bet_amount',
                             'bet_reference', 'bet_transaction_id', 'bet_type', 'total_games', 'live_events',
                             'total_odd', 'possible_win', 'witholding_tax', 'excise_tax', 'bet_attribution',
                             'browser_details', 'extra_data', 'created_by'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            // Validate input data
            $validation = ControllerHelpers::validatePartnerBetData($extractedData);
            if (!$validation['valid']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Validation failed', 'errors' => $validation['errors']], true);
            }

            $partnerId = $extractedData['partner_id'];
            $profileId = $extractedData['profile_id'];
            $betCurrency = $extractedData['bet_currency'] ?: 'KES';
            $betAmount = $extractedData['bet_amount'];
            $betReference = $extractedData['bet_reference'];
            $betTransactionId = $extractedData['bet_transaction_id'];
            $betType = $extractedData['bet_type'] ?: 0;
            $totalGames = $extractedData['total_games'];
            $liveEvents = $extractedData['live_events'] ?: 0;
            $totalOdd = $extractedData['total_odd'];
            $possibleWin = $extractedData['possible_win'];
            $witholdingTax = $extractedData['witholding_tax'] ?: 0.0000;
            $exciseTax = $extractedData['excise_tax'] ?: 0.0000;
            $betAttribution = $extractedData['bet_attribution'] ?: 1;
            $browserDetails = $extractedData['browser_details'];
            $extraData = $extractedData['extra_data'];
            $createdBy = $extractedData['created_by'];

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 1",
                [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Check if bet reference already exists for this partner
            $existingBet = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT bet_id FROM partners_bets WHERE partner_id = :partner_id AND bet_reference = :bet_reference",
                [':partner_id' => $partnerId, ':bet_reference' => $betReference]);

            if ($existingBet) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Bet reference already exists for this partner!'], true);
            }

            // Insert new partner bet
            $betId = $this->rawInsertBulk('dbBetsWrite', 'partners_bets', [
                'partner_id' => $partnerId,
                'profile_id' => $profileId,
                'bet_currency' => $betCurrency,
                'bet_amount' => $betAmount,
                'bet_reference' => $betReference,
                'bet_transaction_id' => $betTransactionId,
                'bet_type' => $betType,
                'total_games' => $totalGames,
                'live_events' => $liveEvents,
                'total_odd' => $totalOdd,
                'possible_win' => $possibleWin,
                'witholding_tax' => $witholdingTax,
                'excise_tax' => $exciseTax,
                'bet_attribution' => $betAttribution,
                'browser_details' => $browserDetails,
                'extra_data' => $extraData,
                'created_by' => $createdBy,
                'status' => 0, // Pending
                'created_at' => $this->now()
            ]);

            if (!$betId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner bet!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetsCache($partnerId);
            RedisUtils::invalidatePartnerCache($partnerId);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner bet created successfully!',
                    'data' => ['bet_id' => $betId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerBets
     * @return type
     */
    function GetPartnerBets()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bets";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBets :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_currency', 'bet_type', 'status', 'start_date', 'end_date', 'profile_id', 'skip_cache'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pb.bet_id', 'DESC');

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_bets', [
                'partner_id' => $params['partner_id'],
                'bet_currency' => $params['bet_currency'],
                'bet_type' => $params['bet_type'],
                'status' => $params['status'],
                'start_date' => $params['start_date'],
                'end_date' => $params['end_date'],
                'profile_id' => $params['profile_id'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            $skipCache = $params['skip_cache'] ?: 0;
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner bets successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            if ($params['partner_id']) {
                $searchParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pb.partner_id = :partner_id";
            }

            if ($params['profile_id']) {
                $searchParams[':profile_id'] = $params['profile_id'];
                $searchQuery .= " AND pb.profile_id = :profile_id";
            }

            if ($params['bet_currency']) {
                $searchParams[':bet_currency'] = $params['bet_currency'];
                $searchQuery .= " AND pb.bet_currency = :bet_currency";
            }

            if ($params['bet_type']) {
                $searchParams[':bet_type'] = $params['bet_type'];
                $searchQuery .= " AND pb.bet_type = :bet_type";
            }

            if ($params['status']) {
                $searchParams[':status'] = $params['status'];
                $searchQuery .= " AND pb.status = :status";
            }

            if ($params['start_date']) {
                $searchParams[':start_date'] = $params['start_date'];
                $searchQuery .= " AND DATE(pb.created_at) >= :start_date";
            }

            if ($params['end_date']) {
                $searchParams[':end_date'] = $params['end_date'];
                $searchQuery .= " AND DATE(pb.created_at) <= :end_date";
            }

            if ($pagination['export'] == 1) {
                // For export, remove pagination and apply export-specific limits
                $orderBy = $searchParams['sort'] ? "ORDER BY " . $searchParams['sort'] . " " . $searchParams['order'] : "ORDER BY pb.bet_id DESC";
                $exportLimit = 100000; // Large limit for exports
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // Execute query with betting-specific context
            $query = "SELECT (SELECT COUNT(pb.bet_id) FROM partners_bets pb $searchQuery) as trx_count,
                      pb.bet_id, pb.partner_id, p.name as partner_name, pb.profile_id, pb.bet_currency,
                      pb.bet_amount, pb.bet_reference, pb.bet_transaction_id, pb.bet_credit_transaction_id,
                      pb.bet_type, pb.total_games, pb.live_events, pb.total_odd, pb.possible_win,
                      pb.witholding_tax, pb.excise_tax, pb.bet_attribution, pb.browser_details,
                      pb.extra_data, pb.created_by, pb.kra_report, pb.risk_state, pb.processed,
                      pb.status, pb.created_at, pb.updated_at,
                      CASE
                        WHEN pb.status = 0 THEN 'Pending'
                        WHEN pb.status = 1 THEN 'Won'
                        WHEN pb.status = 2 THEN 'Lost'
                        WHEN pb.status = 3 THEN 'Cancelled'
                        ELSE 'Unknown'
                      END as status_description
                      FROM partners_bets pb
                      LEFT JOIN partners p ON pb.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bets found.'], true);
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(10)); // 10 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }
    /**
     * GetPartnersBetSlips
     * @return type
     */
    function GetPartnersBetSlips()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bet Slips";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_id', 'sport_id', 'status', 'live_bet', 'skip_cache'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $dateRange = ControllerHelpers::extractDateRange($data);

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_bet_slips', [
                'partner_id' => $params['partner_id'],
                'bet_id' => $params['bet_id'],
                'sport_id' => $params['sport_id'],
                'status' => $params['status'],
                'live_bet' => $params['live_bet'],
                'start_date' => $dateRange['start_date'],
                'end_date' => $dateRange['end_date'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            $skipCache = $params['skip_cache'] ?: 0;
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner bet slips successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $searchQuery .= " AND pbs.partner_id = :partner_id";
                $queryParams[':partner_id'] = $params['partner_id'];
            }

            if ($params['bet_id']) {
                $searchQuery .= " AND pbs.bet_id = :bet_id";
                $queryParams[':bet_id'] = $params['bet_id'];
            }

            if ($params['sport_id']) {
                $searchQuery .= " AND pbs.sport_id = :sport_id";
                $queryParams[':sport_id'] = $params['sport_id'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND pbs.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            if ($params['live_bet'] !== false) {
                $searchQuery .= " AND pbs.live_bet = :live_bet";
                $queryParams[':live_bet'] = $params['live_bet'];
            }

            if ($dateRange['start_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) >= :start_date";
                $queryParams[':start_date'] = $dateRange['start_date'];
            }

            if ($dateRange['end_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) <= :end_date";
                $queryParams[':end_date'] = $dateRange['end_date'];
            }

            // Build sorting
            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'],
                $pagination['page'], $pagination['limit']);

            if ($pagination['export'] == 1) {
                // For export, remove pagination and apply export-specific limits
                $orderBy = $searchParams['sort'] ? "ORDER BY " . $searchParams['sort'] . " " . $searchParams['order'] : "ORDER BY pbs.slip_id DESC";
                $exportLimit = 100000; // Large limit for exports
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pbs.slip_id) FROM partners_bet_slips pbs $searchQuery) as trx_count,
                      pbs.slip_id, pbs.partner_id, p.name as partner_name, pbs.bet_id, pbs.sport_id,
                      pbs.parent_match_id, pbs.parent_market_id, pbs.market_id, pbs.selection_id,
                      pbs.outcome_name, pbs.odd_value, pbs.pick, pbs.pick_name, pbs.winning_outcome,
                      pbs.ht_scores, pbs.ft_scores, pbs.et_scores, pbs.extra_data, pbs.live_bet,
                      pbs.status, pbs.resulting_type, pbs.start_time, pbs.created_at, pbs.updated_at
                      FROM partners_bet_slips pbs
                      LEFT JOIN partners p ON pbs.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found!'], true);
            }

            // Format numeric values
            foreach ($results as &$result) {
                $result['odd_value'] = number_format((float)$result['odd_value'], 2, '.', '');
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(10)); // 10 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Bet Slips successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerBet - Update an existing partner bet
     * @param int $betId
     * @return type
     */
    function UpdatePartnerBet($betId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Bet";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerBet :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'status', 'risk_state', 'processed', 'kra_report'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            $status = $extractedData['status'];
            $riskState = $extractedData['risk_state'];
            $processed = $extractedData['processed'];
            $kraReport = $extractedData['kra_report'];

            // Check if bet exists
            $existingBet = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT bet_id, partner_id FROM partners_bets WHERE bet_id = :bet_id",
                [':bet_id' => $betId]);

            if (!$existingBet) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner bet not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':bet_id' => $betId];

            if ($status !== null) {
                if (!in_array($status, [0, 1, 2, 3])) { // 0=Pending, 1=Won, 2=Lost, 3=Cancelled
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Invalid status value!'], true);
                }
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if ($riskState !== null) {
                if (!is_numeric($riskState)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Risk state must be numeric!'], true);
                }
                $updateFields[] = "risk_state = :risk_state";
                $updateParams[':risk_state'] = $riskState;
            }

            if ($processed !== null) {
                if (!in_array($processed, [0, 1])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Processed must be 0 or 1!'], true);
                }
                $updateFields[] = "processed = :processed";
                $updateParams[':processed'] = $processed;
            }

            if ($kraReport !== null) {
                if (!in_array($kraReport, [0, 1])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'KRA report must be 0 or 1!'], true);
                }
                $updateFields[] = "kra_report = :kra_report";
                $updateParams[':kra_report'] = $kraReport;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateFields[] = "updated_at = :updated_at";
            $updateParams[':updated_at'] = $this->now();

            $updateSql = "UPDATE partners_bets SET " . implode(', ', $updateFields) . " WHERE bet_id = :bet_id";
            $result = $this->rawUpdateWithParams('dbBetsWrite', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner bet!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetsCache($existingBet['partner_id'], $betId);
            RedisUtils::invalidatePartnerCache($existingBet['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner bet updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeletePartnerBet - Delete an existing partner bet
     * @param int $betId
     * @return type
     */
    function DeletePartnerBet($betId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete Partner Bet";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request DeletePartnerBet :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            // Check if bet exists
            $existingBet = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT bet_id, partner_id, status FROM partners_bets WHERE bet_id = :bet_id",
                [':bet_id' => $betId]);

            if (!$existingBet) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner bet not found!'], true);
            }

            // Check if bet can be deleted (only pending bets)
            if ($existingBet['status'] != 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Only pending bets can be deleted!'], true);
            }

            // Delete related bet slips first
            $this->rawUpdateWithParams('dbBetsWrite',
                "DELETE FROM partners_bet_slips WHERE bet_id = :bet_id",
                [':bet_id' => $betId]);

            // Delete the bet
            $result = $this->rawUpdateWithParams('dbBetsWrite',
                "DELETE FROM partners_bets WHERE bet_id = :bet_id",
                [':bet_id' => $betId]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete partner bet!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetsCache($existingBet['partner_id'], $betId);
            RedisUtils::invalidatePartnerBetSlipsCache($existingBet['partner_id']);
            RedisUtils::invalidatePartnerCache($existingBet['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner bet deleted successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerBetSlip - Create a new partner bet slip
     * @return type
     */
    function CreatePartnerBetSlip()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Bet Slip";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerBetSlip :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_id', 'bet_id', 'sport_id', 'parent_match_id',
                             'parent_market_id', 'market_id', 'selection_id', 'outcome_name', 'odd_value',
                             'pick', 'pick_name', 'winning_outcome', 'ht_scores', 'ft_scores', 'et_scores',
                             'extra_data', 'live_bet', 'resulting_type', 'start_time'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            // Validate input data
            $validation = ControllerHelpers::validatePartnerBetSlipData($extractedData);
            if (!$validation['valid']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Validation failed', 'errors' => $validation['errors']], true);
            }

            $partnerId = $extractedData['partner_id'];
            $betId = $extractedData['bet_id'];
            $sportId = $extractedData['sport_id'];
            $parentMatchId = $extractedData['parent_match_id'];
            $parentMarketId = $extractedData['parent_market_id'];
            $marketId = $extractedData['market_id'];
            $selectionId = $extractedData['selection_id'];
            $outcomeName = $extractedData['outcome_name'];
            $oddValue = $extractedData['odd_value'];
            $pick = $extractedData['pick'];
            $pickName = $extractedData['pick_name'];
            $winningOutcome = $extractedData['winning_outcome'];
            $htScores = $extractedData['ht_scores'];
            $ftScores = $extractedData['ft_scores'];
            $etScores = $extractedData['et_scores'];
            $extraData = $extractedData['extra_data'];
            $liveBet = $extractedData['live_bet'] ?: 0;
            $resultingType = $extractedData['resulting_type'];
            $startTime = $extractedData['start_time'];

            // Check if bet exists and belongs to the partner
            $existingBet = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT bet_id FROM partners_bets WHERE bet_id = :bet_id AND partner_id = :partner_id",
                [':bet_id' => $betId, ':partner_id' => $partnerId]);

            if (!$existingBet) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Bet not found or does not belong to this partner!'], true);
            }

            // Insert new bet slip
            $slipId = $this->rawInsertBulk('dbBetsWrite', 'partners_bet_slips', [
                'partner_id' => $partnerId,
                'bet_id' => $betId,
                'sport_id' => $sportId,
                'parent_match_id' => $parentMatchId,
                'parent_market_id' => $parentMarketId,
                'market_id' => $marketId,
                'selection_id' => $selectionId,
                'outcome_name' => $outcomeName,
                'odd_value' => $oddValue,
                'pick' => $pick,
                'pick_name' => $pickName,
                'winning_outcome' => $winningOutcome,
                'ht_scores' => $htScores,
                'ft_scores' => $ftScores,
                'et_scores' => $etScores,
                'extra_data' => $extraData,
                'live_bet' => $liveBet,
                'status' => 0, // Pending
                'resulting_type' => $resultingType,
                'start_time' => $startTime,
                'created_at' => $this->now()
            ]);

            if (!$slipId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner bet slip!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetSlipsCache($partnerId);
            RedisUtils::invalidatePartnerCache($partnerId);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner bet slip created successfully!',
                    'data' => ['slip_id' => $slipId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerBetSlip - Update an existing partner bet slip
     * @param int $slipId
     * @return type
     */
    function UpdatePartnerBetSlip($slipId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Bet Slip";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerBetSlip :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'status', 'winning_outcome', 'ht_scores', 'ft_scores', 'et_scores', 'resulting_type'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            $status = $extractedData['status'];
            $winningOutcome = $extractedData['winning_outcome'];
            $htScores = $extractedData['ht_scores'];
            $ftScores = $extractedData['ft_scores'];
            $etScores = $extractedData['et_scores'];
            $resultingType = $extractedData['resulting_type'];

            // Check if bet slip exists
            $existingSlip = $this->rawSelectOneRecord('dbUser',
                "SELECT slip_id, partner_id FROM partners_bet_slips WHERE slip_id = :slip_id",
                [':slip_id' => $slipId]);

            if (!$existingSlip) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner bet slip not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':slip_id' => $slipId];

            if ($status !== null) {
                if (!is_numeric($status)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Status must be numeric!'], true);
                }
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if ($winningOutcome !== null) {
                $updateFields[] = "winning_outcome = :winning_outcome";
                $updateParams[':winning_outcome'] = $winningOutcome;
            }

            if ($htScores !== null) {
                $updateFields[] = "ht_scores = :ht_scores";
                $updateParams[':ht_scores'] = $htScores;
            }

            if ($ftScores !== null) {
                $updateFields[] = "ft_scores = :ft_scores";
                $updateParams[':ft_scores'] = $ftScores;
            }

            if ($etScores !== null) {
                $updateFields[] = "et_scores = :et_scores";
                $updateParams[':et_scores'] = $etScores;
            }

            if ($resultingType !== null) {
                $updateFields[] = "resulting_type = :resulting_type";
                $updateParams[':resulting_type'] = $resultingType;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateFields[] = "updated_at = :updated_at";
            $updateParams[':updated_at'] = $this->now();

            $updateSql = "UPDATE partners_bet_slips SET " . implode(', ', $updateFields) . " WHERE slip_id = :slip_id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner bet slip!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetSlipsCache($existingSlip['partner_id'], $slipId);
            RedisUtils::invalidatePartnerCache($existingSlip['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner bet slip updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeletePartnerBetSlip - Delete an existing partner bet slip
     * @param int $slipId
     * @return type
     */
    function DeletePartnerBetSlip($slipId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete Partner Bet Slip";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request DeletePartnerBetSlip :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            // Check if bet slip exists
            $existingSlip = $this->rawSelectOneRecord('dbUser',
                "SELECT slip_id, partner_id, bet_id FROM partners_bet_slips WHERE slip_id = :slip_id",
                [':slip_id' => $slipId]);

            if (!$existingSlip) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner bet slip not found!'], true);
            }

            // Check if the parent bet is still pending (only allow deletion of pending bet slips)
            $parentBet = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT status FROM partners_bets WHERE bet_id = :bet_id",
                [':bet_id' => $existingSlip['bet_id']]);

            if ($parentBet && $parentBet['status'] != 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Cannot delete bet slip for non-pending bet!'], true);
            }

            // Delete the bet slip
            $result = $this->rawUpdateWithParams('dbUser',
                "DELETE FROM partners_bet_slips WHERE slip_id = :slip_id",
                [':slip_id' => $slipId]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete partner bet slip!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetSlipsCache($existingSlip['partner_id'], $slipId);
            RedisUtils::invalidatePartnerCache($existingSlip['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner bet slip deleted successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


}
